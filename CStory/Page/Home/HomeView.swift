//
//  HomeView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

/// 主页视图
///
/// 这是一个纯粹的UI组件，它接收一个`HomeVM`对象并将其数据显示出来。
/// 所有的业务逻辑、数据格式化均由VM处理。
/// 使用新架构的MVVM模式，提供现代化的主页体验。
struct HomeView: View {

  // MARK: - 属性

  // 环境变量
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper
  @State private var showingCardDetail = false
  @State private var selectedCard: HomeCardDisplayData?

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // 底部按钮相关状态
  @State private var showCreateTransactionView = false
  @State private var showInsightSheet = false
  @State private var showSettingView = false
  @State private var showCircleExplanationSheet = false

  // View只依赖于VM，不再关心原始数据
  private var viewModel: HomeVM {
    HomeVM(
      dataManager: dataManager,
      onTransactionTap: { transaction in
        // 震动反馈
        hapticManager.trigger(.selection)
        // 导航到交易详情页面
        pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
      }
    )
  }

  // MARK: - 主体视图

  var body: some View {
    ZStack {
//      VStack(spacing: 0) {
        // MARK: 顶部问候语区域
//        HStack {
//          HStack(spacing: 12) {
//            Image("Logo40")
//              .resizable()
//              .frame(width: 40, height: 40)
//              .background(Color.cWhite)
//              .cornerRadius(20)
//            Text(viewModel.greetingMessage)
//              .font(.system(size: 16, weight: .medium))
//              .foregroundColor(.cWhite)
//          }
//          .padding(.trailing, 24)
//          .background(Color.cWhite.opacity(0.4))
//          .cornerRadius(20)
//
//          Spacer()
//        }
//        .padding(.horizontal, 16)
//        .padding(.vertical, 12)

        // MARK: 主要内容区域
        ScrollView(.vertical, showsIndicators: false) {
          VStack(spacing: 0) {
            // MARK: 净资产区域
            VStack(spacing: 0) {
              TitleKit(
                viewModel: TitleKitVM.titleOnly(title: "净资产")
              )
              .opacity(0.6)
              HStack {
                DisplayCurrencyView.size32(
                  symbol: viewModel.netAssetSymbol,
                  amount: viewModel.netAssetAmount)
                Spacer()
                ActionButton(
                  viewModel: ActionButtonVM.viewAllButton {
                    pathManager.path.append(NavigationDestination.cardBagView)
                  }
                )
              }
              .padding(.bottom, 12)
              .padding(.horizontal, 16)
            }

            // MARK: 卡片横向滚动区域
            ScrollView(.horizontal, showsIndicators: false) {
              HStack(spacing: 12) {
                // 显示所有选中的卡片
                ForEach(Array(viewModel.homeCardDisplayData.enumerated()), id: \.offset) {
                  index, cardData in
                  cardView(
                    balance: cardData.balance,
                    symbol: cardData.symbol,
                    isCredit: cardData.isCredit,
                    coverImageName: cardData.coverImageName,
                    textColor: cardData.textColor,
                    bankLogo: cardData.bankLogo
                  )
                  .onTapGesture {
                    hapticManager.trigger(.selection)
                    selectedCard = cardData
                    withAnimation(.easeInOut(duration: 0.3)) {
                      showingCardDetail = true
                    }
                  }
                }

                // 创建卡片按钮
                AddCardButton(
                  viewModel: AddCardButtonVM.minimal {
                    pathManager.path.append(NavigationDestination.cardCategoryView)
                  })
              }
              .padding(.horizontal, 16)
            }

            // MARK: 统计区域
            VStack(spacing: 0) {
              TitleKit(
                viewModel: TitleKitVM.titleOnly(title: "统计")
              )
              HStack(alignment: .center, spacing: 12) {
                // 左侧多圆环进度显示区域（正方形）
                VStack {
                  HomeMultiCircularView(viewModel: viewModel.homeMultiCircularVM)
                }
                .frame(width: 148, height: 148)
                .background(Color.cWhite)
                .cornerRadius(16)
                .overlay(
                  RoundedRectangle(cornerRadius: 16)
                    .strokeBorder(Color.accentColor.opacity(0.08), lineWidth: 1)
                )
                .onTapGesture {
                  hapticManager.trigger(.selection)
                  showCircleExplanationSheet = true
                }

                // 右侧统计卡片区域 - 精确对齐高度
                VStack(spacing: 12) {
                  // 总资产卡片 - 让内容填满高度
                  HomeStatisticCard(viewModel: viewModel.totalAssetCardVM)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)

                  // 总负债卡片 - 让内容填满高度
                  HomeStatisticCard(viewModel: viewModel.totalLiabilityCardVM)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
                .frame(maxWidth: .infinity, maxHeight: 148)
              }
              .padding(.horizontal, 16)
            }

            // MARK: 最近交易区域
            TitleKit(
              viewModel: TitleKitVM(
                title: "最近交易",
                rightButtonTitle: "全部交易",
                rightButtonAction: {
                  pathManager.path.append(NavigationDestination.transactionRecordView)
                }
              )
            )

            // 最近交易列表 - 使用统一的TransactionListContent组件
            VStack(spacing: 0) {
              TransactionListContent(
                transactionDayGroups: viewModel.recentTransactionDayGroups,
                currencySymbol: viewModel.netAssetSymbol
              )
              .padding(.bottom, 100)  // 为浮动按钮留出空间
            }
          }
        }
        .background(Color.cLightBlue)
//        .clipShape(
//          UnevenRoundedRectangle(topLeadingRadius: 36, topTrailingRadius: 36)
//        )
        .edgesIgnoringSafeArea(.bottom)
//      }

      // MARK: 浮动操作按钮
      FloatingActionButtonView(
        buttons: [
          FloatingActionButton(
            iconName: "setting_icon",
            action: {
              hapticManager.trigger(.impactLight)
              showSettingView = true
            }),
          FloatingActionButton(
            title: "记一笔",
            action: {
              hapticManager.trigger(.impactMedium)
              showCreateTransactionView = true
            }, style: .primary),
          FloatingActionButton(
            iconName: "insight_icon",
            action: {
              hapticManager.trigger(.impactLight)
              showInsightSheet = true
            }),
        ]
      )
    }
    .background(.cLightBlue)
//    .background(Color.accentColor)
    // 圆环数据解释Sheet
    .floatingSheet(
      isPresented: $showCircleExplanationSheet,
      config: SheetBase(
        maxDetent: .height(400),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      CircleExplanationSheet(
        savingsRatio: viewModel.savingsRatio,
        creditAvailableRatio: viewModel.creditAvailableRatio,
        cashFlowHealthScore: viewModel.cashFlowHealthScore,
        dismiss: {
          showCircleExplanationSheet = false
        }
      )
    }

    // 卡片详情overlay
    .overlay {
      if showingCardDetail, let selectedCard = selectedCard {
        // 根据selectedCard的id找到对应的真实CardModel
        let realCard = dataManager.cards.first { $0.id == selectedCard.id }

        CardDetailView(
          card: realCard,  // 传递真实的CardModel，与正式首页保持一致
          cardNamespace: nil,
          showingCardDetail: $showingCardDetail,
          pathManager: pathManager,
          transactions: dataManager.allTransactions,
          currencies: dataManager.currencies,
          animationMode: .slideFromTop,
          isCreatingCard: false,
          isCredit: nil,  // 让CardDetailView从CardModel中获取，与正式首页保持一致
          mainCategory: nil,
          subCategory: nil,
          onCardCreated: nil,
          dataManager: dataManager
        )
      }
    }

    // MARK: - Sheet展示

    // 记账页面overlay
    .overlay {
      if showCreateTransactionView {
        CreateTransactionView(
          showCreateTransactionView: $showCreateTransactionView
        )
      }
    }

    // 设置Sheet
    .floatingSheet(
      isPresented: $showSettingView,
      config: SheetBase(
        maxDetent: .fraction(0.99), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SettingView()
    }

    // 洞察Sheet
    .floatingSheet(
      isPresented: $showInsightSheet,
      config: SheetBase(
        maxDetent: .fraction(0.99), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      InsightSheet(dataManager: dataManager)
    }
  }

  // MARK: - 子视图构建器

  /// 卡片视图
  private func cardView(
    balance: Double,
    symbol: String,
    isCredit: Bool,
    coverImageName: String,
    textColor: Color,
    bankLogo: Data?
  ) -> some View {
    VStack(spacing: 0) {
      HStack {
        Spacer()
        // 卡片图标 - 使用银行Logo或默认图标
        cardIcon(bankLogo: bankLogo)
      }
      Spacer()
      HStack {
        DisplayCurrencyView.size12(
          symbol: symbol,
          amount: balance
        )
        .color(textColor)
        Spacer()
      }
    }
    .padding(6)
    .background(
      Image(coverImageName)
        .resizable()
        .aspectRatio(contentMode: .fill)
    )
    .frame(width: 100, height: 60)
    .cornerRadius(16)
  }

  /// 卡片图标
  private func cardIcon(bankLogo: Data?) -> some View {
    Group {
      if let logoData = bankLogo, let uiImage = UIImage(data: logoData) {
        Image(uiImage: uiImage)
          .resizable()
          .aspectRatio(contentMode: .fit)
          .frame(width: 16, height: 16)
          .background(Color.cWhite)
          .cornerRadius(4)
      } else {
        // 当没有logo时显示空白
        Color.clear
          .frame(width: 16, height: 16)
      }
    }
  }

}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct HomeView_Previews: PreviewProvider {
    static var previews: some View {
      HomeView()
        .withDataManager(createPreviewDataManager())
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      // 创建示例货币
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        )
      ]

      // 创建示例卡片
      let cards = [
        CardModel(
          id: UUID(),
          order: 0,
          isCredit: false,
          isSelected: true,
          name: "招商银行储蓄卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 12580.50,
          credit: 0,
          isStatistics: true,
          cover: "card1",
          bankName: "招商银行",
          cardNumber: "1234",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(),
          order: 1,
          isCredit: true,
          isSelected: true,
          name: "工商银行信用卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: -2580.30,
          credit: 10000.0,
          isStatistics: true,
          cover: "card2",
          bankName: "工商银行",
          cardNumber: "5678",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
      ]

      // 创建示例主分类
      let mainCategories = [
        TransactionMainCategoryModel(
          id: "expense_shopping",
          name: "购物",
          icon: .emoji("🛒"),
          order: 0,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "收入",
          icon: .emoji("💰"),
          order: 0,
          type: "income"
        ),
      ]

      // 创建示例子分类
      let subCategories = [
        TransactionSubCategoryModel(
          id: "shopping_daily",
          name: "日常用品",
          icon: .emoji("🧴"),
          order: 0,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "salary_main",
          name: "工资",
          icon: .emoji("💼"),
          order: 0,
          mainId: "income_salary"
        ),
      ]

      // 创建示例交易
      let now = Date()
      let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: now) ?? now
      let recentTransactions = [
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[0].id,
          transactionAmount: 128.50,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "",
          transactionDate: now,
          createdAt: now,
          updatedAt: now
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[1].id,
          transactionAmount: 8500.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "",
          transactionDate: yesterday,
          createdAt: yesterday,
          updatedAt: yesterday
        ),
      ]

      return DataManagement(
        cards: cards,
        mainCategories: mainCategories,
        subCategories: subCategories,
        currencies: currencies,
        recentTransactions: recentTransactions,
        allTransactions: recentTransactions,
        chatMessages: []
      )
    }
  }
#endif
