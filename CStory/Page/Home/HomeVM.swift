//
//  HomeVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

// MARK: - 卡片显示数据结构

/// 卡片显示数据结构，用于UI层展示
///
/// 该结构体包含了UI层显示卡片所需的所有格式化数据，避免UI层直接访问业务模型。
/// 所有的业务逻辑处理（如货币转换、颜色计算等）都在ViewModel层完成。
///
/// - Note: 该结构体遵循MVVM架构原则，将业务逻辑与UI展示分离
struct HomeCardDisplayData: Identifiable {
  /// 卡片唯一标识符
  let id: UUID

  /// 卡片余额（已转换为本位货币）
  let balance: Double

  /// 货币符号（本位货币符号）
  let symbol: String

  /// 是否为信用卡
  let isCredit: Bool

  /// 卡片背景图片名称（由CardCoverHelper处理）
  let coverImageName: String

  /// 文本颜色（根据背景深浅计算）
  let textColor: Color

  /// 银行Logo数据（可选）
  let bankLogo: Data?
}

/// 主页视图模型
///
/// 负责主页的业务逻辑处理和数据管理，提供格式化的UI数据。
/// 主要功能：资产统计、最近交易分组、卡片数据处理、问候语生成。
final class HomeVM: ObservableObject {

  // MARK: - Dependencies

  private let dataManager: DataManagement
  private let onTransactionTap: ((TransactionModel) -> Void)?

  // MARK: - Published Properties

  // 问候语
  @Published var greetingMessage: String

  // 资产统计
  @Published var netAssetAmount: Double
  @Published var netAssetSymbol: String

  // 交易数据
  @Published var recentTransactionDayGroups: [TransactionDayGroupWithRowVM] = []

  // 卡片数据
  @Published var homeCardDisplayData: [HomeCardDisplayData] = []

  // 统计卡片
  @Published var totalAssetCardVM: HomeStatisticCardVM
  @Published var totalLiabilityCardVM: HomeStatisticCardVM
  @Published var homeMultiCircularVM: HomeMultiCircularVM

  // 圆环指标
  @Published var savingsRatio: Double = 0.0
  @Published var creditAvailableRatio: Double = 0.0
  @Published var cashFlowHealthScore: Double = 0.0

  // MARK: - Initialization

  init(dataManager: DataManagement, onTransactionTap: ((TransactionModel) -> Void)? = nil) {
    self.dataManager = dataManager
    self.onTransactionTap = onTransactionTap

    // 初始化默认值
    self.greetingMessage = ""
    self.netAssetAmount = 0.0
    self.netAssetSymbol = ""

    self.totalAssetCardVM = HomeStatisticCardVM(
      title: "总资产", amount: 0.0, currencySymbol: "¥", iconName: "TotalCardsIcon")
    self.totalLiabilityCardVM = HomeStatisticCardVM(
      title: "总负债", amount: 0.0, currencySymbol: "¥", iconName: "TotalLiabilitiesIcon")
    self.homeMultiCircularVM = HomeMultiCircularVM.assetStatistics(
      totalAsset: 0.0, totalLiability: 0.0, netAsset: 0.0, size: 90)

    // 计算实际数据
    calculateData()
  }

  // MARK: - Private Methods

  private func calculateData() {
    calculateGreeting()
    calculateAssetStatistics()
    calculateRecentTransactions()
    calculateHomeCards()
  }

  private func calculateGreeting() {
    let hour = Calendar.current.component(.hour, from: Date())
    switch hour {
    case 5..<12: greetingMessage = "早上好！"
    case 12..<18: greetingMessage = "下午好！"
    case 18..<22: greetingMessage = "晚上好！"
    default: greetingMessage = "夜深了！"
    }
  }

  private func calculateAssetStatistics() {
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode
    netAssetSymbol = dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"

    let result = CurrencyService.shared.calculateCardsDetailed(
      cards: dataManager.cards,
      currencies: dataManager.currencies,
      baseCurrencyCode: baseCurrencyCode,
      filterCreditCard: nil,
      debug: false
    )

    // 更新基础数据
    netAssetAmount = result.netCards
    totalAssetCardVM.amount = result.totalCards
    totalAssetCardVM.currencySymbol = netAssetSymbol
    totalLiabilityCardVM.amount = result.totalLiabilities
    totalLiabilityCardVM.currencySymbol = netAssetSymbol

    // 计算圆环指标
    calculateCircleMetrics(result: result)

    // 更新圆环视图
    homeMultiCircularVM = HomeMultiCircularVM(
      rings: [
        RingData(progress: savingsRatio, color: .accentColor, lineWidth: 8, title: "储蓄净值占比"),
        RingData(progress: creditAvailableRatio, color: .green, lineWidth: 8, title: "信用额度可用率"),
        RingData(progress: cashFlowHealthScore, color: .orange, lineWidth: 8, title: "现金流健康度"),
      ],
      size: 90
    )
  }

  private func calculateCircleMetrics(result: CurrencyService.CardCalculationResult) {
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode

    // 1. 储蓄净值占比
    let savingsNetValue = dataManager.cards
      .filter { !$0.isCredit }
      .compactMap { card in
        let balance = CurrencyService.shared.convertAmount(
          card.balance, from: card.currency, to: baseCurrencyCode,
          currencies: dataManager.currencies)
        return balance > 0 ? balance : nil
      }
      .reduce(0.0) { $0 + $1 }

    savingsRatio = result.totalCards > 0 ? min(1.0, savingsNetValue / result.totalCards) : 0.0

    // 2. 信用额度可用率
    let creditCards = dataManager.cards.filter { $0.isCredit }
    let (totalLimit, usedAmount) = creditCards.reduce((0.0, 0.0)) { result, card in
      let limit = CurrencyService.shared.convertAmount(
        card.credit, from: card.currency, to: baseCurrencyCode, currencies: dataManager.currencies)
      let balance = CurrencyService.shared.convertAmount(
        card.balance, from: card.currency, to: baseCurrencyCode, currencies: dataManager.currencies)
      return (result.0 + limit, result.1 + (balance < 0 ? abs(balance) : 0))
    }

    creditAvailableRatio =
      totalLimit > 0 ? min(1.0, max(0.0, (totalLimit - usedAmount) / totalLimit)) : 0.0

    // 3. 现金流健康度
    cashFlowHealthScore = calculateCashFlowHealth()
  }

  private func calculateCashFlowHealth() -> Double {
    let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
    let recentTransactions = TransactionQueryService.shared.filterTransactions(
      dataManager.allTransactions.filter { $0.transactionDate >= thirtyDaysAgo },
      byTypes: [.income, .expense, .transfer, .refund]
    )

    let (totalIncome, totalExpense) = recentTransactions.reduce((0.0, 0.0)) { result, transaction in
      let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction, targetCurrency: CurrencyService.shared.baseCurrencyCode)
      return (result.0 + amounts.income, result.1 + amounts.expense)
    }

    let totalCashFlow = totalIncome + totalExpense
    return totalCashFlow > 0 ? min(1.0, totalIncome / totalCashFlow) : 0.0
  }

  private func calculateRecentTransactions() {
    let calendar = Calendar.current
    let today = Date()

    guard let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: today) else {
      recentTransactionDayGroups = []
      return
    }

    // 获取并筛选最近7天的交易
    let recent7DaysTransactions = TransactionQueryService.shared
      .filterTransactions(
        dataManager.recentTransactions, byTypes: [.income, .expense, .transfer, .refund]
      )
      .filter { $0.transactionDate >= sevenDaysAgo && $0.transactionDate <= today }
      .sorted { $0.transactionDate > $1.transactionDate }

    guard !recent7DaysTransactions.isEmpty else {
      recentTransactionDayGroups = []
      return
    }

    // 按日期分组并转换为UI数据
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode
    let groupedByDate = Dictionary(grouping: recent7DaysTransactions) {
      calendar.startOfDay(for: $0.transactionDate)
    }

    recentTransactionDayGroups = groupedByDate.map { date, transactions in
      let (dayIncome, dayExpense) = transactions.reduce((0.0, 0.0)) { result, transaction in
        let amounts = CurrencyService.shared.getTransactionIncomeExpenseAmounts(
          transaction: transaction, targetCurrency: baseCurrencyCode)
        return (result.0 + amounts.income, result.1 + amounts.expense)
      }

      let transactionRowVMs =
        transactions
        .sorted { $0.transactionDate > $1.transactionDate }
        .map { transaction in
          TransactionRowVM(
            transaction: transaction,
            relatedCard: dataManager.findCard(by: transaction.fromCardId)
              ?? dataManager.findCard(by: transaction.toCardId),
            categoryInfo: dataManager.getCategoryInfo(for: transaction.transactionCategoryId),
            onTap: { self.onTransactionTap?(transaction) }
          )
        }

      return (
        date: date,
        group: TransactionDayGroupWithRowVM(
          dateText: formatDateText(date),
          dayIncome: dayIncome,
          dayExpense: dayExpense,
          transactionRowVMs: transactionRowVMs
        )
      )
    }
    .sorted { $0.date > $1.date }
    .map { $0.group }
  }

  private func calculateHomeCards() {
    homeCardDisplayData = dataManager.cards
      .filter { $0.isSelected }
      .sorted { $0.order < $1.order }
      .map { card in
        let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
        let coverImageName = CardCoverHelper.shared.getCoverImageName(for: coverType)
        let isDark = CardCoverHelper.shared.isCoverDark(for: coverType)

        return HomeCardDisplayData(
          id: card.id,
          balance: card.balance,
          symbol: card.symbol,
          isCredit: card.isCredit,
          coverImageName: coverImageName,
          textColor: isDark ? .white : .cBlack,
          bankLogo: card.bankLogo
        )
      }
  }

  private func formatDateText(_ date: Date) -> String {
    return TransactionDisplayService.shared.formatTransactionTime(date, style: .dateOnly)
  }

}
