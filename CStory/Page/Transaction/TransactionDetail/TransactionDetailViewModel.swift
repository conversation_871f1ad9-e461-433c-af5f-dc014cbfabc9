//
//  TransactionDetailViewModel.swift
//  CStory
//
//  Created by NZUE on 2025/5/27.
//

import Foundation
import SwiftData
import SwiftUI

// MARK: - 卡片显示模式枚举
enum CardDisplayMode {
  case single(card: CardModel?, type: String)  // 单卡片显示
  case transfer(from: CardModel?, to: CardModel?)  // 转账双卡片显示
}

// MARK: - 交易详情视图模型
/// 处理交易详情的展示、编辑和汇率计算。
final class TransactionDetailViewModel: ObservableObject {
  // MARK: - 数据结构
  struct TransactionDetailData {
    let transactionId: String
    let transactionType: TransactionType
    let transactionTitle: String
    let formattedDate: String
    let currencySymbol: String
    let currencyCode: String

    // 交易明细
    let subtotal: Double
    let discountAmount: Double
    let refundAmount: Double
    let totalAmount: Double

    // 标签状态
    let hasRefund: Bool
    let hasDiscount: Bool

    // 分类信息
    let categoryName: String
    let categoryIcon: IconType

    // 卡片信息
    let fromCard: CardModel?  // 支出卡片
    let toCard: CardModel?  // 收入卡片
    let cardDisplayMode: CardDisplayMode

    // 退款记录
    let refundTransactions: [TransactionModel]

    // 汇率信息
    let exchangeRateInfo: ExchangeRateInfo?

    // 原始交易数据
    let originalTransaction: TransactionModel
  }

  // MARK: - 汇率信息结构
  struct ExchangeRateInfo {
    let baseCurrencyCode: String
    let baseCurrencySymbol: String
    let transactionCurrencyCode: String
    let transactionCurrencySymbol: String
    let exchangeRate: Double  // 1 交易货币 = ? 本位币
    let isBaseCurrency: Bool  // 交易货币是否就是本位币
    let formattedRate: String  // 格式化的汇率显示
  }

  // MARK: - 编辑数据结构
  struct EditingTransactionData {
    var amount: Double
    var discountAmount: Double
    var date: Date
    var categoryId: String?
    var fromCardId: UUID?
    var toCardId: UUID?
    var note: String

    init(from transaction: TransactionModel) {
      self.amount = transaction.transactionAmount
      self.discountAmount = transaction.discountAmount ?? 0
      self.date = transaction.transactionDate
      self.categoryId = transaction.transactionCategoryId
      self.fromCardId = transaction.fromCardId
      self.toCardId = transaction.toCardId
      self.note = transaction.remark
    }
  }

  // MARK: - 数字键盘编辑类型
  enum NumericEditType {
    case amount
    case discount

    var title: String {
      switch self {
      case .amount: return "编辑小计"
      case .discount: return "编辑优惠"
      }
    }

    var fieldName: String {
      switch self {
      case .amount: return "小计"
      case .discount: return "优惠"
      }
    }
  }

  // MARK: - 卡片编辑类型
  enum CardEditType {
    case from
    case to
  }

  // MARK: - 发布属性
  @Published var detailData: TransactionDetailData?
  @Published var errorMessage: String?

  // MARK: - 编辑状态
  @Published var isEditing = false
  @Published var editingData: EditingTransactionData?

  // MARK: - UI 状态
  @Published var showTimeSheet = false
  @Published var showNumericKeypad = false
  @Published var showCategorySheet = false
  @Published var showCardSheet = false
  @Published var numericKeypadText = ""
  @Published var currentNumericEditType: NumericEditType = .amount
  @Published var currentEditingCardType: CardEditType = .from

  // MARK: - 删除状态
  @Published var showDeleteAlert = false
  @Published var deleteErrorMessage = ""
  @Published var showDeleteErrorAlert = false

  // MARK: - 数据依赖
  private let dataManager: DataManagement
  private let transaction: TransactionModel

  // MARK: - Initialization

  init(transaction: TransactionModel, dataManager: DataManagement) {
    self.transaction = transaction
    self.dataManager = dataManager

    // 自动加载数据
    loadData()
  }

  // MARK: - 公共方法

  /// 重新加载数据
  func loadData() {
    errorMessage = nil
    let data = calculateDetailData(transaction: transaction)
    self.detailData = data
  }

  /// 准备交易详情数据（兼容版本 - 保持向后兼容）
  /// - Parameters:
  ///   - transaction: 交易记录
  ///   - cards: 资产列表
  ///   - transactions: 所有交易列表
  ///   - categories: 分类列表
  ///   - subCategories: 子分类列表
  ///   - currencies: 货币列表
  ///   - debug: 是否输出调试信息
  func prepareData(
    transaction: TransactionModel,
    cards: [CardModel],
    transactions: [TransactionModel],
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel],
    currencies: [CurrencyModel],
    debug: Bool = false
  ) {
    errorMessage = nil

    let data = calculateDetailData(
      transaction: transaction,
      cards: cards,
      transactions: transactions,
      categories: categories,
      subCategories: subCategories,
      currencies: currencies
    )

    self.detailData = data

  }

  // MARK: - 私有方法
  /// 计算交易详情数据
  private func calculateDetailData(transaction: TransactionModel) -> TransactionDetailData {
    return calculateDetailData(
      transaction: transaction,
      cards: dataManager.cards,
      transactions: dataManager.allTransactions,
      categories: dataManager.mainCategories,
      subCategories: dataManager.subCategories,
      currencies: dataManager.currencies
    )
  }

  /// 计算交易详情数据（兼容版本）
  private func calculateDetailData(
    transaction: TransactionModel,
    cards: [CardModel],
    transactions: [TransactionModel],
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel],
    currencies: [CurrencyModel]
  ) -> TransactionDetailData {
    // 1. 基本信息计算
    let transactionId = "交易 #\(String(transaction.id.uuidString.prefix(6)).uppercased())"
    let formattedDate = DateFormattingHelper.shared.smartFormat(date: transaction.transactionDate)
    let currencySymbol = transaction.symbol
    let currencyCode = transaction.currency

    // 2. 金额计算
    let subtotal = transaction.transactionAmount
    let discountAmount = transaction.discountAmount ?? 0
    let refundAmount = transaction.refundAmount ?? 0
    let totalAmount = subtotal - discountAmount - refundAmount

    // 3. 状态标签 - 使用 TransactionDisplayService
    let hasRefund = TransactionDisplayService.shared.hasRefundTag(for: transaction)
    let hasDiscount = TransactionDisplayService.shared.hasDiscountTag(for: transaction)

    // 4. 分类信息 - 使用 TransactionDisplayService
    let categoryInfo = TransactionDisplayService.shared.getCategoryInfo(
      for: transaction,
      mainCategories: categories,
      subCategories: subCategories
    )
    let categoryName = categoryInfo.name
    let categoryIcon = categoryInfo.icon ?? .emoji("📝")  // 提供默认图标

    // 5. 卡片信息
    let (fromCard, toCard, cardDisplayMode) = getCardInfo(
      transaction: transaction, cards: cards)

    // 6. 退款记录
    let refundTransactions = getRefundTransactions(
      originalTransactionId: transaction.id,
      transactions: transactions
    )

    // 8. 计算汇率信息
    let exchangeRateInfo = calculateExchangeRateInfo(
      transaction: transaction,
      transactionCurrency: currencyCode,
      transactionSymbol: currencySymbol,
      currencies: currencies
    )

    return TransactionDetailData(
      transactionId: transactionId,
      transactionType: transaction.transactionType,
      transactionTitle: transactionId,
      formattedDate: formattedDate,
      currencySymbol: currencySymbol,
      currencyCode: currencyCode,
      subtotal: subtotal,
      discountAmount: discountAmount,
      refundAmount: refundAmount,
      totalAmount: totalAmount,
      hasRefund: hasRefund,
      hasDiscount: hasDiscount,
      categoryName: categoryName,
      categoryIcon: categoryIcon,
      fromCard: fromCard,
      toCard: toCard,
      cardDisplayMode: cardDisplayMode,
      refundTransactions: refundTransactions,
      exchangeRateInfo: exchangeRateInfo,
      originalTransaction: transaction
    )
  }

  /// 获取分类信息（优化版本 - 统一逻辑）
  private func getCategoryInfo(
    transaction: TransactionModel,
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> (name: String, icon: IconType) {
    return getCategoryInfo(
      from: transaction.transactionCategoryId,
      categories: categories,
      subCategories: subCategories
    )
  }

  /// 根据分类ID获取分类信息（统一方法）
  private func getCategoryInfo(
    from categoryId: String?,
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> (name: String, icon: IconType) {
    guard let categoryId = categoryId else {
      return ("未知类别", IconType.emoji("❓"))
    }

    // 先尝试在子分类中查找
    if let subCategory = subCategories.first(where: { $0.id == categoryId }) {
      // 查找对应的主分类
      if let mainCategory = categories.first(where: { $0.id == subCategory.mainId }) {
        return ("\(mainCategory.name)-\(subCategory.name)", subCategory.icon)
      } else {
        return (subCategory.name, subCategory.icon)
      }
    }

    // 再在主分类中查找
    if let mainCategory = categories.first(where: { $0.id == categoryId }) {
      return (mainCategory.name, mainCategory.icon)
    }

    return ("未知类别", IconType.emoji("❓"))
  }

  /// 获取卡片信息
  private func getCardInfo(transaction: TransactionModel, cards: [CardModel]) -> (
    CardModel?, CardModel?, CardDisplayMode
  ) {
    let fromCard =
      dataManager.findCard(by: transaction.fromCardId)
      ?? transaction.fromCardId.flatMap { id in cards.first { $0.id == id } }
    let toCard =
      dataManager.findCard(by: transaction.toCardId)
      ?? transaction.toCardId.flatMap { id in cards.first { $0.id == id } }

    switch transaction.transactionType {
    case .expense:
      return (fromCard, toCard, .single(card: fromCard, type: "支出卡片"))
    case .income:
      return (fromCard, toCard, .single(card: toCard, type: "收入卡片"))
    case .transfer:
      return (fromCard, toCard, .transfer(from: fromCard, to: toCard))
    case .refund:
      let refundCard = toCard ?? fromCard
      return (fromCard, toCard, .single(card: refundCard, type: "退款卡片"))
    case .createCard:
      return (fromCard, toCard, .single(card: toCard, type: "创建卡片"))
    case .adjustCard:
      let adjustCard = fromCard ?? toCard
      return (fromCard, toCard, .single(card: adjustCard, type: "调整卡片"))
    }
  }

  /// 获取退款记录
  private func getRefundTransactions(
    originalTransactionId: UUID,
    transactions: [TransactionModel]
  ) -> [TransactionModel] {
    // 使用 TransactionQueryService 查询退款交易
    let refundTransactions = TransactionQueryService.shared.filterTransactions(
      transactions,
      byTypes: [.refund]
    ).filter { $0.originalTransactionId == originalTransactionId }

    return refundTransactions.sorted { $0.transactionDate > $1.transactionDate }
  }

  /// 计算汇率信息
  private func calculateExchangeRateInfo(
    transaction: TransactionModel,
    transactionCurrency: String,
    transactionSymbol: String,
    currencies: [CurrencyModel]
  ) -> ExchangeRateInfo? {
    // 获取本位币信息
    guard let baseCurrency = currencies.first(where: { $0.isBaseCurrency }) else {
      return nil
    }

    // 如果交易货币就是本位币，不需要显示汇率
    if transactionCurrency == baseCurrency.code {
      return ExchangeRateInfo(
        baseCurrencyCode: baseCurrency.code,
        baseCurrencySymbol: baseCurrency.symbol,
        transactionCurrencyCode: transactionCurrency,
        transactionCurrencySymbol: transactionSymbol,
        exchangeRate: 1.0,
        isBaseCurrency: true,
        formattedRate: "本位币"
      )
    }

    // 使用交易记录中存储的历史汇率：1 交易货币 = ? 本位币
    let exchangeRate: Double
    switch transaction.transactionType {
    case .income:
      exchangeRate = transaction.incomeToBaseRate
    case .expense:
      exchangeRate = transaction.expenseToBaseRate
    case .transfer:
      // 转账交易使用支出汇率（从源卡片转出的角度）
      exchangeRate = transaction.expenseToBaseRate
    case .refund:
      // 退款交易使用收入汇率（退回到卡片的角度）
      exchangeRate = transaction.incomeToBaseRate
    case .createCard:
      // 创建卡片交易使用收入汇率（添加初始余额的角度）
      exchangeRate = transaction.incomeToBaseRate
    case .adjustCard:
      // 调整卡片余额交易：根据交易金额正负决定使用哪个汇率
      if transaction.transactionAmount >= 0 {
        // 正数表示增加余额，使用收入汇率
        exchangeRate = transaction.incomeToBaseRate
      } else {
        // 负数表示减少余额，使用支出汇率
        exchangeRate = transaction.expenseToBaseRate
      }
    }

    // 格式化汇率显示
    let formattedRate = formatExchangeRate(
      rate: exchangeRate,
      fromSymbol: transactionSymbol,
      toSymbol: baseCurrency.symbol
    )

    return ExchangeRateInfo(
      baseCurrencyCode: baseCurrency.code,
      baseCurrencySymbol: baseCurrency.symbol,
      transactionCurrencyCode: transactionCurrency,
      transactionCurrencySymbol: transactionSymbol,
      exchangeRate: exchangeRate,
      isBaseCurrency: false,
      formattedRate: formattedRate
    )
  }

  /// 格式化汇率显示
  private func formatExchangeRate(rate: Double, fromSymbol: String, toSymbol: String) -> String {
    // 使用NumberFormatService格式化汇率，保留适当的小数位数
    let rateString = NumberFormatService.shared.formatAmount(rate, maxDecimals: 6, minDecimals: 2)

    return "1\(fromSymbol) = \(rateString)\(toSymbol)"
  }

  // MARK: - 编辑功能方法

  /// 进入编辑模式
  func enterEditingMode() {
    guard let data = detailData else { return }
    editingData = EditingTransactionData(from: data.originalTransaction)
    isEditing = true
  }

  /// 取消编辑
  func cancelEditing() {
    isEditing = false
    editingData = nil
    closeAllSheets()
  }

  /// 关闭所有 Sheet
  private func closeAllSheets() {
    showTimeSheet = false
    showNumericKeypad = false
    showCategorySheet = false
    showCardSheet = false
  }

  // MARK: - UI 交互方法

  /// 打开数字键盘
  func openNumericKeypad(for type: NumericEditType) {
    currentNumericEditType = type

    switch type {
    case .amount:
      let currentValue = editingData?.amount ?? 0
      numericKeypadText = NumberFormatService.shared.formatAmountForInput(currentValue)
    case .discount:
      let currentValue = editingData?.discountAmount ?? 0
      numericKeypadText = NumberFormatService.shared.formatAmountForInput(currentValue)
    }

    showNumericKeypad = true
  }

  /// 保存数字输入
  func saveNumericInput() {
    guard let newValue = Double(numericKeypadText) else {
      print("无效的数字输入: \(numericKeypadText)")
      return
    }

    switch currentNumericEditType {
    case .amount:
      editingData?.amount = newValue
    case .discount:
      editingData?.discountAmount = newValue
    }

    print("保存数字输入 - 类型: \(currentNumericEditType.fieldName), 值: \(newValue)")
    showNumericKeypad = false
  }

  /// 保存卡片选择
  func saveCardSelection(cardId: UUID?) {
    updateEditingDataWithSelectedCard(cardId)
    showCardSheet = false
  }

  /// 更新编辑数据，根据选择的资产ID
  private func updateEditingDataWithSelectedCard(_ cardId: UUID?) {
    guard let cardId = cardId,
      let originalTransaction = detailData?.originalTransaction
    else { return }

    print(
      "开始更新编辑数据 - 原始fromCardId: \(editingData?.fromCardId?.uuidString ?? "nil"), 原始toCardId: \(editingData?.toCardId?.uuidString ?? "nil")"
    )

    // 根据交易类型和当前编辑的卡片类型决定更新哪个资产ID
    switch originalTransaction.transactionType {
    case .expense:
      // 支出只有支出卡片
      editingData?.fromCardId = cardId

    case .income, .refund:
      // 收入和退款只有收入卡片
      editingData?.toCardId = cardId

    case .transfer:
      // 转账根据当前编辑的卡片类型决定
      switch currentEditingCardType {
      case .from:
        editingData?.fromCardId = cardId
      case .to:
        editingData?.toCardId = cardId
      }

    case .createCard:
      // 创建更新收入卡片
      editingData?.toCardId = cardId

    case .adjustCard:
      // 调整根据当前编辑的卡片类型决定
      switch currentEditingCardType {
      case .from:
        editingData?.fromCardId = cardId
      case .to:
        editingData?.toCardId = cardId
      }
    }

    print(
      "更新编辑数据完成 - 交易类型: \(originalTransaction.transactionType), 卡片类型: \(currentEditingCardType), 选择资产ID: \(cardId)"
    )
    print(
      "更新后fromCardId: \(editingData?.fromCardId?.uuidString ?? "nil"), 更新后toCardId: \(editingData?.toCardId?.uuidString ?? "nil")"
    )
  }

  // MARK: - 计算属性

  /// 是否可以退款
  var canRefund: Bool {
    guard let data = detailData else { return false }
    return data.transactionType == .expense && data.totalAmount > 0
  }

  /// 当前交易的基本信息（计算属性）
  var transactionBasicInfo: (id: String, date: String, symbol: String, code: String)? {
    guard let data = detailData else { return nil }
    return (
      id: data.transactionId,
      date: data.formattedDate,
      symbol: data.currencySymbol,
      code: data.currencyCode
    )
  }

  /// 当前交易的金额信息（计算属性）
  var transactionAmountInfo: (subtotal: Double, discount: Double, refund: Double, total: Double)? {
    guard let data = detailData else { return nil }
    return (
      subtotal: data.subtotal,
      discount: data.discountAmount,
      refund: data.refundAmount,
      total: data.totalAmount
    )
  }

  /// 当前交易的状态标签（计算属性）
  var transactionStatusFlags: (hasRefund: Bool, hasDiscount: Bool)? {
    guard let data = detailData else { return nil }
    return (hasRefund: data.hasRefund, hasDiscount: data.hasDiscount)
  }

  /// 当前交易的分类信息（计算属性）
  var transactionCategoryInfo: (name: String, icon: IconType)? {
    guard let data = detailData else { return nil }
    return (name: data.categoryName, icon: data.categoryIcon)
  }

  /// 当前交易的卡片信息（计算属性）
  var transactionCardInfo: (fromCard: CardModel?, toCard: CardModel?, displayMode: CardDisplayMode)?
  {
    guard let data = detailData else { return nil }
    return (fromCard: data.fromCard, toCard: data.toCard, displayMode: data.cardDisplayMode)
  }

  /// 当前交易的汇率信息（计算属性）
  var transactionExchangeRateInfo: TransactionDetailViewModel.ExchangeRateInfo? {
    return detailData?.exchangeRateInfo
  }

  /// 当前交易的退款记录（计算属性）
  var transactionRefundRecords: [TransactionModel] {
    return detailData?.refundTransactions ?? []
  }

  /// 获取当前显示的数据（编辑状态下使用编辑数据，否则使用原始数据）
  /// 优化版本 - 自动使用内部数据源
  var currentDisplayData: TransactionDetailData? {
    guard let originalData = detailData else { return nil }

    if isEditing, let editingData = editingData {
      return createDisplayDataFromEditing(
        editingData: editingData,
        originalData: originalData,
        cards: dataManager.cards,
        categories: dataManager.mainCategories,
        subCategories: dataManager.subCategories
      )
    } else {
      return originalData
    }
  }

  /// 获取过滤后的分类列表
  var filteredCategories: [TransactionMainCategoryModel] {
    // 按交易类型过滤分类
    return dataManager.mainCategories
      .filter { $0.type == transaction.transactionType.rawValue }
      .sorted { $0.order < $1.order }
  }

  /// 获取当前显示的数据（兼容版本 - 保持向后兼容）
  func getCurrentDisplayData(
    cards: [CardModel],
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> TransactionDetailData? {
    guard let originalData = detailData else { return nil }

    if isEditing, let editingData = editingData {
      return createDisplayDataFromEditing(
        editingData: editingData,
        originalData: originalData,
        cards: cards,
        categories: categories,
        subCategories: subCategories
      )
    } else {
      return originalData
    }
  }

  /// 从编辑数据创建显示数据
  private func createDisplayDataFromEditing(
    editingData: EditingTransactionData,
    originalData: TransactionDetailData,
    cards: [CardModel],
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> TransactionDetailData {
    // 获取分类信息
    let (categoryName, categoryIcon) = getCategoryInfoForEditing(
      categoryId: editingData.categoryId,
      transactionType: originalData.transactionType,
      categories: categories,
      subCategories: subCategories
    )

    // 获取卡片信息
    let (fromCard, toCard, cardDisplayMode) = getCardInfoForEditing(
      fromCardId: editingData.fromCardId,
      toCardId: editingData.toCardId,
      transactionType: originalData.transactionType,
      cards: cards
    )

    return TransactionDetailData(
      transactionId: originalData.transactionId,
      transactionType: originalData.transactionType,
      transactionTitle: originalData.transactionTitle,
      formattedDate: DateFormattingHelper.shared.smartFormat(date: editingData.date),
      currencySymbol: originalData.currencySymbol,
      currencyCode: originalData.currencyCode,
      subtotal: editingData.amount,
      discountAmount: editingData.discountAmount,
      refundAmount: originalData.refundAmount,
      totalAmount: editingData.amount - editingData.discountAmount - originalData.refundAmount,
      hasRefund: originalData.refundAmount > 0,
      hasDiscount: editingData.discountAmount > 0,
      categoryName: categoryName,
      categoryIcon: categoryIcon,
      fromCard: fromCard,
      toCard: toCard,
      cardDisplayMode: cardDisplayMode,
      refundTransactions: originalData.refundTransactions,
      exchangeRateInfo: originalData.exchangeRateInfo,
      originalTransaction: originalData.originalTransaction
    )
  }

  /// 获取编辑状态下的分类信息（使用统一方法）
  private func getCategoryInfoForEditing(
    categoryId: String?,
    transactionType: TransactionType,
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> (name: String, icon: IconType) {
    return getCategoryInfo(
      from: categoryId,
      categories: categories,
      subCategories: subCategories
    )
  }

  /// 获取编辑状态下的卡片信息
  private func getCardInfoForEditing(
    fromCardId: UUID?,
    toCardId: UUID?,
    transactionType: TransactionType,
    cards: [CardModel]
  ) -> (CardModel?, CardModel?, CardDisplayMode) {
    let fromCard =
      dataManager.findCard(by: fromCardId)
      ?? fromCardId.flatMap { id in cards.first { $0.id == id } }
    let toCard =
      dataManager.findCard(by: toCardId)
      ?? toCardId.flatMap { id in cards.first { $0.id == id } }

    switch transactionType {
    case .expense:
      return (fromCard, toCard, .single(card: fromCard, type: "支出卡片"))
    case .income:
      return (fromCard, toCard, .single(card: toCard, type: "收入卡片"))
    case .transfer:
      return (fromCard, toCard, .transfer(from: fromCard, to: toCard))
    case .refund:
      let refundCard = toCard ?? fromCard
      return (fromCard, toCard, .single(card: refundCard, type: "退款卡片"))
    case .createCard:
      return (fromCard, toCard, .single(card: toCard, type: "创建卡片"))
    case .adjustCard:
      let adjustCard = fromCard ?? toCard
      return (fromCard, toCard, .single(card: adjustCard, type: "调整卡片"))
    }
  }

  // MARK: - 业务操作方法

  /// 处理退款操作
  func handleRefundAction() -> NavigationDestination? {
    guard canRefund else { return nil }
    return NavigationDestination.transactionRefundView(transaction: transaction)
  }

  /// 处理删除操作
  func handleDeleteAction(modelContext: ModelContext) -> Bool {
    do {
      var allCardIds: [UUID?] = []

      if transaction.transactionType == .refund {
        // 删除退款交易
        allCardIds = deleteSingleRefundTransaction(modelContext: modelContext)
      } else {
        // 删除原始交易（同时删除相关退款）
        allCardIds = deleteOriginalTransactionWithRefunds(modelContext: modelContext)
      }

      // 删除主交易记录
      modelContext.delete(transaction)
      try modelContext.save()

      print("✅ 交易删除成功")

      // 使用统一的服务方法重新计算受影响的卡片余额
      BalanceRecalculationService.shared.recalculateBalances(
        for: allCardIds,
        modelContext: modelContext,
        currencies: dataManager.currencies,
        operation: "交易删除"
      )

      return true
    } catch {
      print("❌ 交易删除失败: \(error.localizedDescription)")
      deleteErrorMessage = "删除失败: \(error.localizedDescription)"
      showDeleteErrorAlert = true
      return false
    }
  }

  /// 处理保存操作
  func handleSaveAction(modelContext: ModelContext) -> Bool {
    // 验证编辑数据
    let validation = validateEditingData()
    guard validation.isValid else {
      errorMessage = validation.errorMessage
      return false
    }

    // 记录编辑前的卡片ID
    let originalFromCardId = transaction.fromCardId
    let originalToCardId = transaction.toCardId

    // 保存编辑的交易
    let success = saveEditedTransaction(modelContext: modelContext)
    if success {
      // 获取编辑后的卡片ID
      let newFromCardId = transaction.fromCardId
      let newToCardId = transaction.toCardId

      // 使用统一的服务方法重新计算受影响的卡片余额
      BalanceRecalculationService.shared.recalculateBalances(
        for: [originalFromCardId, originalToCardId, newFromCardId, newToCardId],
        modelContext: modelContext,
        currencies: dataManager.currencies,
        operation: "交易更新"
      )

      // 重新加载数据以反映更改
      loadData()
      return true
    }
    return false
  }

  /// 根据分类ID获取分类信息（公共方法 - 使用统一逻辑）
  func getCategoryInfo(
    from categoryId: String?,
    transactionType: TransactionType,
    categories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> (name: String, icon: IconType) {
    return getCategoryInfo(
      from: categoryId,
      categories: categories,
      subCategories: subCategories
    )
  }

  // MARK: - 保存编辑方法

  /// 保存编辑的交易数据（优化版本 - 使用内部数据源）
  /// - Parameters:
  ///   - modelContext: SwiftData模型上下文
  /// - Returns: 是否保存成功
  func saveEditedTransaction(modelContext: ModelContext) -> Bool {
    return saveEditedTransaction(
      modelContext: modelContext,
      cards: dataManager.cards,
      currencies: dataManager.currencies
    )
  }

  /// 保存编辑的交易数据（兼容版本）
  /// - Parameters:
  ///   - modelContext: SwiftData模型上下文
  ///   - cards: 卡片列表（用于汇率重新计算）
  ///   - currencies: 货币列表（用于汇率重新计算）
  /// - Returns: 是否保存成功
  func saveEditedTransaction(
    modelContext: ModelContext,
    cards: [CardModel],
    currencies: [CurrencyModel]
  ) -> Bool {
    guard let editingData = editingData,
      let originalTransaction = detailData?.originalTransaction
    else {
      print("❌ 没有编辑数据或原始交易数据")
      return false
    }

    do {
      // 特殊处理退款交易
      if originalTransaction.transactionType == .refund {
        return saveEditedRefundTransaction(
          editingData: editingData,
          refundTransaction: originalTransaction,
          modelContext: modelContext
        )
      }

      // 处理普通交易
      // 更新原始交易的属性
      originalTransaction.transactionAmount = editingData.amount
      originalTransaction.discountAmount =
        editingData.discountAmount > 0 ? editingData.discountAmount : nil
      originalTransaction.transactionDate = editingData.date
      originalTransaction.transactionCategoryId = editingData.categoryId
      originalTransaction.fromCardId = editingData.fromCardId
      originalTransaction.toCardId = editingData.toCardId
      originalTransaction.remark = editingData.note
      originalTransaction.updatedAt = Date()

      // 只有在货币相关的变更时才需要重新计算汇率，保护历史汇率数据
      let needsRateRecalculation = CurrencyService.shared.shouldUpdateExchangeRates(
        originalTransaction: originalTransaction,
        newFromCardId: editingData.fromCardId,
        newToCardId: editingData.toCardId,
        cards: cards
      )

      if needsRateRecalculation {
        print("💱 检测到货币相关变更，需要重新计算汇率")
        CurrencyService.shared.updateTransactionExchangeRatesIfNeeded(
          transaction: originalTransaction,
          cards: cards,
          currencies: currencies
        )
      } else {
        print("📝 未检测到货币相关变更，保持原有汇率不变")
      }

      // 保存到数据库
      try modelContext.save()

      print("✅ 交易编辑保存成功")

      // 退出编辑模式
      isEditing = false
      self.editingData = nil
      closeAllSheets()

      return true
    } catch {
      print("❌ 交易编辑保存失败: \(error.localizedDescription)")
      errorMessage = "保存失败: \(error.localizedDescription)"
      return false
    }
  }

  /// 保存编辑的退款交易
  /// - Parameters:
  ///   - editingData: 编辑数据
  ///   - refundTransaction: 退款交易
  ///   - modelContext: 数据库上下文
  /// - Returns: 是否保存成功
  private func saveEditedRefundTransaction(
    editingData: EditingTransactionData,
    refundTransaction: TransactionModel,
    modelContext: ModelContext
  ) -> Bool {
    do {
      // 1. 获取原始交易
      guard let originalTransactionId = refundTransaction.originalTransactionId else {
        print("❌ 退款交易缺少原始交易ID")
        errorMessage = "退款交易数据异常"
        return false
      }

      // 查找原始交易
      let fetchDescriptor = FetchDescriptor<TransactionModel>(
        predicate: #Predicate<TransactionModel> { transaction in
          transaction.id == originalTransactionId
        }
      )

      guard let originalTransaction = try modelContext.fetch(fetchDescriptor).first else {
        print("❌ 找不到原始交易")
        errorMessage = "找不到关联的原始交易"
        return false
      }

      // 2. 计算退款金额变化
      let oldRefundAmount = refundTransaction.transactionAmount
      let newRefundAmount = editingData.amount
      let refundAmountDiff = newRefundAmount - oldRefundAmount

      // 3. 更新退款交易
      refundTransaction.transactionAmount = newRefundAmount
      refundTransaction.transactionDate = editingData.date
      refundTransaction.fromCardId = editingData.fromCardId
      refundTransaction.toCardId = editingData.toCardId
      refundTransaction.remark = editingData.note
      refundTransaction.updatedAt = Date()

      // 4. 更新原始交易的退款金额
      let currentRefundAmount = originalTransaction.refundAmount ?? 0
      originalTransaction.refundAmount = currentRefundAmount + refundAmountDiff

      // 5. 保存到数据库
      try modelContext.save()

      print("✅ 退款交易编辑保存成功")
      print("   - 退款金额变化: \(oldRefundAmount) -> \(newRefundAmount)")
      print("   - 原始交易退款总额: \(originalTransaction.refundAmount ?? 0)")

      // 6. 退出编辑模式
      isEditing = false
      self.editingData = nil
      closeAllSheets()

      return true

    } catch {
      print("❌ 退款交易编辑保存失败: \(error.localizedDescription)")
      errorMessage = "保存失败: \(error.localizedDescription)"
      return false
    }
  }

  /// 验证编辑数据是否有效
  func validateEditingData() -> (isValid: Bool, errorMessage: String?) {
    guard let editingData = editingData else {
      return (false, "没有编辑数据")
    }

    // 检查交易金额
    guard editingData.amount > 0 else {
      return (false, "交易金额必须大于0")
    }

    // 检查优惠金额不能大于交易金额
    guard editingData.discountAmount <= editingData.amount else {
      return (false, "优惠金额不能大于交易金额")
    }

    // 根据交易类型检查必要字段
    if let originalTransaction = detailData?.originalTransaction {
      switch originalTransaction.transactionType {
      case .expense:
        // 支出交易需要分类和支出卡片（可选）
        guard editingData.categoryId != nil else {
          return (false, "支出交易必须选择分类")
        }

      case .income:
        // 收入交易需要分类和收入卡片（可选）
        guard editingData.categoryId != nil else {
          return (false, "收入交易必须选择分类")
        }

      case .transfer:
        // 转账交易需要转出和转入卡片
        guard editingData.fromCardId != nil && editingData.toCardId != nil else {
          return (false, "转账交易必须选择转出和转入卡片")
        }

        guard editingData.fromCardId != editingData.toCardId else {
          return (false, "转出和转入卡片不能相同")
        }

      case .refund:
        // 退款交易需要退款接收卡片
        guard editingData.toCardId != nil else {
          return (false, "退款交易必须选择退款接收卡片")
        }

        // 验证退款金额不能超过原始交易的可退款金额
        if originalTransaction.originalTransactionId != nil {
          // 这里可以添加更复杂的验证逻辑，比如检查退款金额是否超过原始交易金额
          // 暂时先做基本验证
        }

      default:
        break
      }
    }

    return (true, nil)
  }

  // MARK: - 删除操作私有方法

  /// 删除单个退款交易并更新原始交易的退款金额
  /// - Returns: 受影响的卡片ID列表
  private func deleteSingleRefundTransaction(modelContext: ModelContext) -> [UUID?] {
    guard transaction.transactionType == .refund else {
      print("❌ 不是退款交易，无法使用此方法删除")
      return []
    }

    guard let originalTransactionId = transaction.originalTransactionId else {
      print("❌ 退款交易缺少原始交易ID")
      return []
    }

    // 1. 查找原始交易
    guard
      let originalTransaction = dataManager.allTransactions.first(where: {
        $0.id == originalTransactionId
      })
    else {
      print("❌ 未找到原始交易: \(originalTransactionId)")
      return []
    }

    // 2. 记录退款交易的卡片ID
    var refundCardIds: [UUID?] = []
    if let fromCardId = transaction.fromCardId {
      refundCardIds.append(fromCardId)
    }
    if let toCardId = transaction.toCardId {
      refundCardIds.append(toCardId)
    }

    // 3. 更新原始交易的退款金额
    let refundAmount = transaction.transactionAmount
    let currentRefundAmount = originalTransaction.refundAmount ?? 0
    originalTransaction.refundAmount = max(0, currentRefundAmount - refundAmount)
    originalTransaction.updatedAt = Date()

    print(
      "📝 删除单个退款交易，更新原始交易退款金额: \(currentRefundAmount) -> \(originalTransaction.refundAmount ?? 0)")

    return refundCardIds
  }

  /// 删除原始交易及其相关退款交易
  /// - Returns: 受影响的卡片ID列表
  private func deleteOriginalTransactionWithRefunds(modelContext: ModelContext) -> [UUID?] {
    let fromCardId = transaction.fromCardId
    let toCardId = transaction.toCardId

    // 查找相关退款交易 - 使用 TransactionQueryService
    let refundTransactions = TransactionQueryService.shared.filterTransactions(
      dataManager.allTransactions,
      byTypes: [.refund]
    )
    let relatedRefunds = refundTransactions.filter { refundTransaction in
      refundTransaction.originalTransactionId == self.transaction.id
    }

    var refundCardIds: [UUID?] = []

    // 删除退款交易
    for refundTransaction in relatedRefunds {
      // 记录退款交易的卡片ID
      if let fromCardId = refundTransaction.fromCardId {
        refundCardIds.append(fromCardId)
      }
      if let toCardId = refundTransaction.toCardId {
        refundCardIds.append(toCardId)
      }

      print("🗑️ 删除相关退款交易: \(refundTransaction.id), 金额: \(refundTransaction.transactionAmount)")
      modelContext.delete(refundTransaction)
    }

    // 合并所有受影响的卡片ID
    var allCardIds: [UUID?] = [fromCardId, toCardId]
    allCardIds.append(contentsOf: refundCardIds)

    return allCardIds
  }
}
