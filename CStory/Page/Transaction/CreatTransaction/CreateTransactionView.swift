//
//  CreateTransactionView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 创建交易遮罩视图容器
///
/// 作为手动记账和AI记账的统一容器，负责模式切换、动画管理和两个子组件的协调。
/// 通过顶部控制栏在手动记账和AI记账之间无缝切换，每个模式都有独立的组件实现。
/// 遵循新架构的MVVM模式，所有数据处理都通过ViewModel完成。
///
/// ## 架构设计
/// - 容器模式：负责模式切换和动画协调
/// - ManualTransactionView：处理手动记账的所有逻辑
/// - AITransactionView：处理AI记账的所有逻辑
/// - 统一的动画系统：与CardDetailView保持一致
/// - MVVM架构：View层只负责UI展示，业务逻辑在ViewModel处理
struct CreateTransactionView: View {
  // MARK: - Environment and Dependencies
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager  // 仅用于配置 ViewModel
  @EnvironmentObject private var pathManager: PathManagerHelper
  @Binding var showCreateTransactionView: Bool

  // MARK: - Initialization

  init(showCreateTransactionView: Binding<Bool>) {
    self._showCreateTransactionView = showCreateTransactionView
  }

  // MARK: - UI状态管理
  @State private var showAIMode = false

  // MARK: - 动画状态
  @State private var showBackground: Bool = false
  @State private var showTopContent: Bool = false  // 顶部内容：交易类型选择器/AI聊天记录
  @State private var showBottomContent: Bool = false  // 底部内容：主内容区域/AI输入区域

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  var body: some View {
    ZStack {
      backgroundView

      // 手动记账界面
      VStack(spacing: 0) {
        // 手动记账模式不需要顶部间距

        ManualTransactionView(
          showTopContent: $showTopContent,
          showBottomContent: $showBottomContent,
          onTransactionSaved: {
            closeTransactionView()
          },
          onCloseRequested: {
            closeTransactionView()
          }
        )
      }
      .offset(x: showAIMode ? -UIScreen.main.bounds.width : 0)
      .animation(.easeInOut(duration: 0.3), value: showAIMode)
      .animation(.easeInOut(duration: 0.4), value: showTopContent)
      .animation(.easeInOut(duration: 0.25), value: showBottomContent)

      // AI记账界面
      VStack(spacing: 0) {

        AITransactionView(
          showTopContent: $showTopContent,
          showBottomContent: $showBottomContent,
          onSwitchToManual: {
            withAnimation(.easeInOut(duration: 0.3)) {
              showAIMode = false
            }
          },
          onCloseRequested: {
            closeTransactionView()
          }
        )
      }
      .offset(x: showAIMode ? 0 : UIScreen.main.bounds.width)
      .animation(.easeInOut(duration: 0.3), value: showAIMode)
      .animation(.easeInOut(duration: 0.4), value: showTopContent)
      .animation(.easeInOut(duration: 0.25), value: showBottomContent)
    }
    .onAppear {
      setupAnimations()
    }
    .onReceive(NotificationCenter.default.publisher(for: .switchToAIMode)) { _ in
      // 从手动记账模式切换到AI记账模式
      hapticManager.trigger(.impactMedium)
      withAnimation(.easeInOut(duration: 0.3)) {
        showAIMode = true
      }
    }
  }

  // MARK: - View Components

  private var backgroundView: some View {
    ZStack {
      // 半透明遮罩
      Color.black.opacity(0.3)
        .opacity(showBackground ? 1 : 0)
        .ignoresSafeArea()
        .onTapGesture {
          hapticManager.trigger(.impactLight)
          closeTransactionView()
        }

      // 统一的玻璃背景
      Rectangle()
        .fill(.regularMaterial)
        .opacity(showBackground ? 1 : 0)
        .ignoresSafeArea()
    }
  }

  // MARK: - Animation and Helper Methods

  private func setupAnimations() {
    // 背景渐入 - 与卡片详情保持一致的0.3s
    withAnimation(.easeOut(duration: 0.3)) {
      showBackground = true
    }

    // 顶部内容从上方滑入 - 对应CardDetailView的卡片动画
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
      withAnimation(.easeInOut(duration: 0.4)) {
        showTopContent = true
      }
    }

    // 底部内容从下方滑入 - 对应CardDetailView的详情内容动画
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
      withAnimation(.easeInOut(duration: 0.25)) {
        showBottomContent = true
      }
    }
  }

  private func closeTransactionView() {
    // 第一阶段：底部内容向下滑出 - 对应CardDetailView的详情内容
    withAnimation(.easeOut(duration: 0.2)) {
      showBottomContent = false
    }

    // 第二阶段：顶部内容向上滑出 - 对应CardDetailView的卡片
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
      withAnimation(.easeOut(duration: 0.3)) {
        showTopContent = false
      }
    }

    // 第三阶段：背景渐出并关闭 - 与卡片详情一致
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) {
      withAnimation(.easeOut(duration: 0.15)) {
        showBackground = false
        showCreateTransactionView = false
      }
    }
  }
}

// MARK: - Notification Extensions

extension Notification.Name {
  static let switchToAIMode = Notification.Name("switchToAIMode")
}

#Preview {
  CreateTransactionView(showCreateTransactionView: .constant(true))
    .environment(
      \.dataManager,
      DataManagement(
        cards: [],
        mainCategories: [],
        subCategories: [],
        currencies: [],
        recentTransactions: [],
        allTransactions: []
      )
    )
    .modelContainer(try! ModelContainer(for: TransactionModel.self))
}
