//
//  TransactionRefundView.swift
//  CStory
//
//  Created by NZUE on 2025/3/10.
//

import SwiftData
import SwiftUI

struct TransactionRefundView: View {
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // 使用 ViewModel 管理业务逻辑
  @ObservedObject private var viewModel: TransactionRefundVM

  // MARK: - Sheet 状态管理
  @State private var isCardSheetPresented = false
  @State private var showingNumericKeypad = false
  @State private var isTimeSheetPresented = false

  // MARK: - 触觉反馈
  private let hapticManager = HapticFeedbackManager.shared

  init(transaction: TransactionModel, dataManager: DataManagement) {
    self.viewModel = TransactionRefundVM(
      transaction: transaction,
      dataManager: dataManager
    )
  }

  // MARK: - 顶部区域
  private var topArea: some View {
    VStack(spacing: 8) {
      // 退款标题
      HStack(spacing: 12) {
        Text("退款申请")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.cBlack)
          .frame(maxWidth: .infinity, alignment: .leading)

        HStack(spacing: 4) {
          Circle()
            .frame(width: 6, height: 6)
            .foregroundColor(.cAccentGreen)
          Text("退款")
            .font(.system(size: 13))
            .foregroundColor(.cBlack.opacity(0.6))
        }
      }

      // 退款时间
      HStack(spacing: 12) {
        Text(DateFormattingHelper.shared.smartFormat(date: viewModel.refundDate))
          .font(.system(size: 15))
          .foregroundColor(.cBlack.opacity(0.6))

        Button(action: {
          hapticManager.trigger(.selection)
          isTimeSheetPresented = true
        }) {
          Image("edit_icon")
            .font(.system(size: 16))
            .foregroundColor(.cAccentBlue)
        }

        Spacer()
      }
    }
    .padding(.vertical, 24)
    .padding(.horizontal, 16)
  }

  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        /// 导航栏
        NavigationBarKit(
          viewModel: NavigationBarKitVM(
            title: "退款申请",
            backAction: {
              dismiss()
            },
            rightButton: .icon(
              "checkmark",
              style: viewModel.canSave ? .primary : .default,
              action: {
                if viewModel.canSave {
                  hapticManager.trigger(.success)
                  handleRefundSave()
                }
              }
            )
          ))

        // 顶部区域
        topArea

        // 主要内容区域
        ScrollView(.vertical, showsIndicators: false) {
          VStack(spacing: 0) {

            // 原始交易信息区域
            DividerTitleKit(title: "原始交易")
              .padding(.horizontal, 16)

            Button(action: {
              hapticManager.trigger(.impactLight)
              dismiss()
            }) {
              HStack(spacing: 12) {
                // 分类图标
                IconView(
                  viewModel: IconViewVM(
                    icon: viewModel.categoryIcon,
                    size: 40,
                    fontSize: 20,
                    backgroundColor: Color.cAccentBlue.opacity(0.1),
                    cornerRadius: 20
                  ))

                VStack(alignment: .leading, spacing: 4) {
                  Text(viewModel.originalCategoryName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.cBlack)
                  Text(
                    DateFormattingHelper.shared.smartFormat(
                      date: viewModel.transaction.transactionDate)
                  )
                  .font(.system(size: 13))
                  .foregroundColor(.cBlack.opacity(0.6))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                  DisplayCurrencyView.size14(
                    symbol: viewModel.transaction.symbol,
                    amount: abs(viewModel.transaction.transactionAmount)
                  )
                  .weight(symbol: .medium, integer: .medium)

                  Text(viewModel.originalCardName)
                    .font(.system(size: 13))
                    .foregroundColor(.cBlack.opacity(0.6))
                }
              }
              .padding(.horizontal, 24)

            }
            .buttonStyle(PlainButtonStyle())

            // 退款金额区域
            DividerTitleKit(title: "退款金额")
              .padding(.horizontal, 16)

            RefundAmountRowView(
              title: "退款金额",
              currencySymbol: viewModel.transaction.symbol,
              amount: Double(viewModel.refundAmount) ?? 0,
              isEditing: true,
              onEditTap: {
                hapticManager.trigger(.selection)
                showingNumericKeypad = true
              }
            )
            .padding(.horizontal, 24)

            // 退款卡片区域
            DividerTitleKit(title: "退款卡片")
              .padding(.horizontal, 16)

            RefundCardView(
              selectedCardId: viewModel.selectedCardId,
              viewModel: viewModel,
              isEditing: true,
              onEditCard: {
                hapticManager.trigger(.selection)
                isCardSheetPresented = true
              }
            )

            Spacer()
          }
        }
        .background(Color.cWhite)
        .clipShape(
          UnevenRoundedRectangle(topLeadingRadius: 36, topTrailingRadius: 36)
        )
        .edgesIgnoringSafeArea(.bottom)

      }
    }
    .background(
      DottedGridBackground()
        .ignoresSafeArea(.all)
    )
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)

    // 浮动面板
    .floatingSheet(
      isPresented: $isCardSheetPresented,
      config: SheetBase(
        maxDetent: .fraction(0.45),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCardSheet(
        title: "选择卡片",
        selectedCardId: $viewModel.selectedCardId,
        cards: dataManager.cards,
        onCardSelected: { cardId in
          hapticManager.trigger(.selection)
          viewModel.selectedCardId = cardId
          isCardSheetPresented = false
        },
        onCancel: {
          hapticManager.trigger(.impactLight)
          isCardSheetPresented = false
        }
      )
    }
    .floatingSheet(
      isPresented: $showingNumericKeypad,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      NumericKeypad(
        text: $viewModel.refundAmount,
        onSave: {
          hapticManager.trigger(.impactMedium)
          showingNumericKeypad = false
        },
        allowNegative: false,  // 退款金额不允许负数
        maxDecimalPlaces: 2  // 普通金额使用2位小数
      )
      .background(Color.cBeige)
    }
    .floatingSheet(
      isPresented: $isTimeSheetPresented,
      config: SheetBase(
        maxDetent: .fraction(0.45),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectTimeSheet(
        title: "选择退款日期",
        selectedDate: $viewModel.refundDate,
        onConfirm: {
          hapticManager.trigger(.selection)
          isTimeSheetPresented = false
        },
        onCancel: {
          hapticManager.trigger(.impactLight)
          isTimeSheetPresented = false
        }
      )
    }
    .alert("操作失败", isPresented: $viewModel.showErrorAlert) {
      Button("确定", role: .cancel) {}
    } message: {
      Text(viewModel.errorMessage)
    }
  }

  // MARK: - 退款处理方法

  /// 处理退款保存
  private func handleRefundSave() {
    if viewModel.handleRefundSave(modelContext: modelContext) {
      // 触觉反馈
      hapticManager.trigger(.success)
      // 关闭页面
      dismiss()
    }
  }

}

// MARK: - 退款金额行组件
private struct RefundAmountRowView: View {
  let title: String
  let currencySymbol: String
  let amount: Double
  let isEditing: Bool
  let onEditTap: (() -> Void)?

  var body: some View {
    HStack(spacing: 0) {
      Text(title)
        .font(.system(size: 14, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.4))
      Spacer()
      HStack(spacing: 6) {
        DisplayCurrencyView.size20(
          symbol: currencySymbol,
          amount: amount
        )
        .foregroundColor(.cBlack)
        .contentTransition(.numericText(value: amount))
        .animation(.easeInOut(duration: 0.3), value: amount)

        if isEditing && onEditTap != nil {
          Image("edit_icon")
            .font(.system(size: 16))
            .foregroundColor(.cAccentBlue)
            .transition(.move(edge: .trailing).combined(with: .opacity))
        }
      }
    }
    .contentShape(Rectangle())
    .onTapGesture {
      if isEditing {
        onEditTap?()
      }
    }
    .padding(.vertical, 20)
  }
}

// MARK: - 退款卡片组件
private struct RefundCardView: View {
  let selectedCardId: UUID?
  let viewModel: TransactionRefundVM
  let isEditing: Bool
  let onEditCard: (() -> Void)?

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  var body: some View {
    HStack(spacing: 12) {
      // 卡片图标
      let (cardName, cardImage) = viewModel.getCardInfo(for: selectedCardId)
      IconView(
        viewModel: IconViewVM(
          icon: cardImage.map { .image($0) } ?? .emoji("❓"),
          size: 40,
          fontSize: 20,
          backgroundColor: Color.cAccentBlue.opacity(0.1),
          cornerRadius: 12
        ))

      VStack(alignment: .leading, spacing: 4) {
        Text(cardName)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)
        Text("退款卡片")
          .font(.system(size: 13, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))
      }

      Spacer()

      // 右侧内容
      HStack(spacing: 12) {
        // 余额信息
        if let balance = viewModel.getCardBalance(for: selectedCardId) {
          VStack(alignment: .trailing, spacing: 4) {
            DisplayCurrencyView.size14(symbol: "¥", amount: balance)
              .simpleFormat()
              .weight(integer: .medium)
              .color(.cBlack)
            Text("余额")
              .font(.system(size: 13))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }

        // 编辑按钮
        if isEditing {
          Button(action: {
            hapticManager.trigger(.selection)
            onEditCard?()
          }) {
            Image("edit_icon")
              .font(.system(size: 16))
              .foregroundColor(.cAccentBlue)
          }
          .transition(.move(edge: .trailing).combined(with: .opacity))
        }
      }
    }
    .padding(.horizontal, 24)
    .contentShape(Rectangle())
    .onTapGesture {
      if isEditing {
        hapticManager.trigger(.selection)
        onEditCard?()
      }
    }
  }
}
