//
//  InsightsView.swift
//  CStory
//
//  Created by house on 2024/9/21.
//

/// 数据分析视图
///
/// 该视图负责展示用户的资产和交易数据分析，主要功能包括：
/// - 资产变动趋势分析
/// - 交易流水趋势分析
/// - 交互式数据可视化
///
/// ## 主要功能
/// - 资产分析
///   - 总资产趋势
///   - 负债趋势
///   - 净资产趋势
/// - 交易分析
///   - 收入趋势
///   - 支出趋势
///   - 净流入趋势
/// - 数据交互
///   - 支持图表拖拽查看具体数据
///   - 动态切换不同数据维度
///   - 平滑的动画过渡效果
///
/// ## 使用示例
/// ```swift
/// InsightSheet()
///     .modelContainer(for: [CardModel.self, TransactionModel.self, TransactionMainCategoryModel.self, TransactionSubCategoryModel.self, CurrencyModel.self])
/// ```
///
/// - Note: 所有金额显示都使用本位币格式化，多货币交易使用历史汇率转换
/// - Important: 图表数据会随资产和交易的变化自动更新
///
import Charts
import Foundation
import SwiftData
import SwiftUI

// MARK: - Extensions

extension CGRect {
  var midPoint: CGPoint {
    CGPoint(x: midX, y: midY)
  }
}

// MARK: - InsightSheet

/// 数据分析视图
struct InsightSheet: View {
  // MARK: - Properties

  @EnvironmentObject var pathManager: PathManagerHelper
  /// 导航路径管理器
  @Environment(\.modelContext) private var modelContext
  /// 数据上下文 (仅用于写操作)
  @Environment(\.dataManager) private var dataManager
  /// 集中式数据管理器

  // MARK: - ViewModel

  @ObservedObject private var viewModel: InsightVM
  /// 数据分析视图模型

  // MARK: - View States

  @GestureState private var isDragging = false
  /// 是否正在拖动图表
  @Namespace private var animation
  /// 命名空间

  // MARK: - 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - Initialization

  init(dataManager: DataManagement) {
    self.viewModel = InsightVM(dataManager: dataManager)
  }

  // MARK: - Computed Properties

  /// 当前时间段的起止日期
  var dateRange: (start: Date, end: Date) {
    viewModel.dateRange
  }

  /// 获取当前选中的交易数据数组
  private var currentTransactionData: [TransactionDataPoint] {
    viewModel.currentTransactionData
  }

  /// 获取当前选中的资产数据数组
  private var currentCardData: [CardDataPoint] {
    viewModel.currentCardData
  }

  /// 获取当前分类统计数据
  private var currentCategoryStats: [CategoryStat] {
    return viewModel.currentCategoryStats
  }

  // MARK: - Chart Helpers

  /// 获取图表X轴的时间单位
  private var chartTimeUnit: Calendar.Component {
    switch viewModel.selectedPeriod {
    case .week, .month: return .day
    case .year: return .month
    }
  }

  /// 获取图表X轴的刻度数量
  private var chartStepCount: Int {
    switch viewModel.selectedPeriod {
    case .week: return 7
    case .month: return 6
    case .year: return 12
    }
  }

  /// 获取图表X轴的日期格式
  private var chartDateFormat: Date.FormatStyle {
    switch viewModel.selectedPeriod {
    case .week, .month: return Date.FormatStyle.dateTime.day()
    case .year: return Date.FormatStyle.dateTime.month()
    }
  }

  @Environment(\.dismiss) private var dismiss
  /// 关闭视图的环境变量

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // 标题栏
      ZStack(alignment: .top) {

        // 添加渐变模糊效果
        VariableBlurView(maxBlurRadius: 15, direction: .blurredTopClearBottom, startOffset: -0.05)
          .ignoresSafeArea()
          .frame(height: 60)

        HStack {
          Text("分析")
            .font(
              .system(size: 28, weight: .medium)
            )
            .foregroundColor(.cBlack)

          Spacer()

          Button(action: {
            hapticManager.trigger(.impactLight)
            dismiss()
          }) {
            Image(systemName: "xmark.circle.fill")
              .foregroundColor(
                Color.cBlack.opacity(0.4)
              )
              .font(
                .system(size: 20, weight: .medium))
          }

        }
        .padding(16)
      }
      .zIndex(1)

      // 主内容
      ScrollView(showsIndicators: false) {
        LazyVStack(spacing: 12) {
          // 使用 TimeControl 组件进行时间选择 - 使用 ViewModel 模式
          TimeControl(viewModel: viewModel.timeControlVM, style: .default)
            .padding(.horizontal, 16)
            .padding(.bottom, 12)

          // 收支统计卡片
          financialSummaryCard

          // 分类占比卡片
          categoryStatsCard

          cardChartCard
          transactionChartCard
        }
        .padding(.vertical, 12)
      }
      .scrollClipDisabled()
    }

    .onAppear {
      viewModel.loadData()
      withAnimation(.easeInOut(duration: 1.0)) {
        viewModel.animationProgress = 1.0
      }
      viewModel.updateSelectedPoints()
    }
  }

  // MARK: - View Components

  /// 收支统计卡片
  @ViewBuilder
  private var financialSummaryCard: some View {
    VStack(alignment: .leading, spacing: 16) {
      Text("收支统计")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.cBlack)

      HStack(spacing: 12) {
        // 支出统计
        VStack(alignment: .leading, spacing: 4) {
          Text("支出")
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
          DisplayCurrencyView.size16(
            symbol: "¥",
            amount: viewModel.financialSummary.totalExpense
          )
          .weight(symbol: .semibold, integer: .semibold)
          .color(Color.cAccentRed, secondary: Color.cAccentRed.opacity(0.8))
        }
        .frame(maxWidth: .infinity, alignment: .leading)

        // 收入统计
        VStack(alignment: .leading, spacing: 4) {
          Text("收入")
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
          DisplayCurrencyView.size16(
            symbol: "¥",
            amount: viewModel.financialSummary.totalIncome
          )
          .weight(symbol: .semibold, integer: .semibold)
          .color(Color.cAccentGreen, secondary: Color.cAccentGreen.opacity(0.8))
        }
        .frame(maxWidth: .infinity, alignment: .leading)
      }

      HStack(spacing: 12) {
        // 结余统计
        VStack(alignment: .leading, spacing: 4) {
          Text("结余")
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
          DisplayCurrencyView.size16(
            symbol: "¥",
            amount: viewModel.financialSummary.balance
          )
          .weight(symbol: .semibold, integer: .semibold)
          .color(
            viewModel.financialSummary.balance >= 0
              ? Color.cAccentGreen : Color.cAccentRed,
            secondary: (viewModel.financialSummary.balance >= 0
              ? Color.cAccentGreen : Color.cAccentRed).opacity(0.8)
          )
        }
        .frame(maxWidth: .infinity, alignment: .leading)

        // 记账次数
        VStack(alignment: .leading, spacing: 4) {
          Text("记账次数")
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
          Text("\(viewModel.financialSummary.transactionCount)次")
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.cAccentBlue)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
      }
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
  }

  /// 分类占比卡片
  @ViewBuilder
  private var categoryStatsCard: some View {
    VStack(alignment: .leading, spacing: 16) {
      // 标题和切换按钮
      HStack {
        Text("分类占比")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        // 切换按钮
        Button(action: {
          hapticManager.trigger(.selection)
          withAnimation(.easeInOut(duration: 0.3)) {
            let currentIndex =
              CategoryDisplayMode.allCases.firstIndex(of: viewModel.categoryDisplayMode) ?? 0
            let nextIndex = (currentIndex + 1) % CategoryDisplayMode.allCases.count
            let nextMode = CategoryDisplayMode.allCases[nextIndex]
            viewModel.selectCategoryMode(nextMode)
          }
        }) {
          HStack(spacing: 6) {
            Image(systemName: viewModel.categoryDisplayMode.icon)
              .foregroundColor(viewModel.categoryDisplayMode.color)
              .font(.system(size: 14, weight: .medium))

            Text(viewModel.categoryDisplayMode.rawValue)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(viewModel.categoryDisplayMode.color)
          }
          .padding(.horizontal, 12)
          .padding(.vertical, 6)
          .background(viewModel.categoryDisplayMode.color.opacity(0.1))
          .cornerRadius(12)
        }
      }

      // 图表和图例
      HStack(spacing: 24) {
        // 饼图
        VStack {
          if currentCategoryStats.isEmpty {
            Circle()
              .fill(Color.cAccentBlue.opacity(0.1))
              .frame(width: 160, height: 160)
              .overlay(
                VStack(spacing: 4) {
                  Image(systemName: "chart.pie")
                    .font(.system(size: 24))
                    .foregroundColor(.cBlack.opacity(0.3))
                  Text("暂无数据")
                    .font(.system(size: 14))
                    .foregroundColor(.cBlack.opacity(0.4))
                }
              )
          } else {
            Chart(currentCategoryStats) { stat in
              SectorMark(
                angle: .value("金额", stat.amount),
                innerRadius: .ratio(0.4),
                angularInset: 1.5
              )
              .foregroundStyle(stat.color)
            }
            .frame(width: 160, height: 160)
          }
        }

        // 图例 - 可滑动
        ScrollView(.vertical, showsIndicators: false) {
          VStack(alignment: .leading, spacing: 8) {
            ForEach(currentCategoryStats) { stat in
              HStack(spacing: 8) {
                Circle()
                  .fill(stat.color)
                  .frame(width: 10, height: 10)

                VStack(alignment: .leading, spacing: 2) {
                  Text(stat.name)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.cBlack)
                    .lineLimit(1)

                  HStack(spacing: 4) {
                    DisplayCurrencyView.size12(
                      symbol: "¥",
                      amount: stat.amount
                    )
                    .showingDecimals(false)
                    .simpleFormat()
                    .weight(symbol: .regular, integer: .regular)
                    .color(Color.cBlack.opacity(0.7))

                    Text("(\(stat.percentage * 100, specifier: "%.1f")%)")
                      .font(.system(size: 12))
                      .foregroundColor(.cBlack.opacity(0.5))
                  }
                }

                Spacer()
              }
            }
          }
          .padding(.vertical, 2)
        }
        .frame(maxWidth: .infinity, maxHeight: 160, alignment: .leading)
      }
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
  }

  /// 资产变动图表卡片
  @ViewBuilder
  private var cardChartCard: some View {
    VStack(alignment: .leading, spacing: 12) {
      cardChartHeader
      cardDataDisplay
      cardChart
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
  }

  /// 资产图表头部
  @ViewBuilder
  private var cardChartHeader: some View {
    HStack {
      Text("资产变动")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.cBlack)
      Spacer()
      ForEach([CardChartType.total, .debt, .net], id: \.self) { type in
        Button(action: {
          hapticManager.trigger(.selection)
          withAnimation {
            viewModel.selectCardType(type)
          }
        }) {
          Text(type.title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(
              viewModel.selectedCardType == type ? type.color : Color.cBlack.opacity(0.6))
        }
      }
    }
  }

  /// 资产数据显示
  @ViewBuilder
  private var cardDataDisplay: some View {
    HStack {
      Text(viewModel.selectedCardPoint?.date.formatted(.dateTime.day().month()) ?? "")
        .foregroundColor(.cBlack.opacity(0.6))
        .font(.system(size: 14))
      Spacer()
      DisplayCurrencyView.size14(
        symbol: "¥",
        amount: viewModel.selectedCardPoint?.amount ?? 0
      )
      .simpleFormat()
      .weight(symbol: .regular, integer: .regular)
      .color(Color.cBlack.opacity(0.6))
    }
    .contentTransition(.numericText(value: viewModel.selectedCardPoint?.amount ?? 0))
    .animation(.easeInOut(duration: 0.3), value: viewModel.selectedCardPoint?.amount)
  }

  /// 资产图表
  @ViewBuilder
  private var cardChart: some View {
    Chart {
      ForEach(currentCardData) { dataPoint in
        LineMark(
          x: .value("日期", dataPoint.date),
          y: .value("金额", dataPoint.amount * viewModel.animationProgress)
        )
        .foregroundStyle(viewModel.selectedCardType.color)
        .interpolationMethod(.catmullRom)

        AreaMark(
          x: .value("日期", dataPoint.date),
          y: .value("金额", dataPoint.amount * viewModel.animationProgress)
        )
        .foregroundStyle(
          .linearGradient(
            colors: [
              viewModel.selectedCardType.color.opacity(0.3),
              viewModel.selectedCardType.color.opacity(0.1),
            ],
            startPoint: .top,
            endPoint: .bottom
          )
        )
        .interpolationMethod(.catmullRom)

        if let selected = viewModel.selectedCardPoint, selected.date == dataPoint.date {
          PointMark(
            x: .value("日期", dataPoint.date),
            y: .value("金额", dataPoint.amount)
          )
          .foregroundStyle(viewModel.selectedCardType.color)
          .symbolSize(100)
        }
      }
      if let selectedPoint = viewModel.selectedCardPoint {
        RuleMark(x: .value("Selected", selectedPoint.date))
          .foregroundStyle(Color.cBlack.opacity(0.3))
          .lineStyle(StrokeStyle(lineWidth: 1))
      }
    }
    .frame(height: 200)
    .chartXAxis {
      AxisMarks(values: .stride(by: chartTimeUnit, count: chartStepCount)) { value in
        AxisGridLine()
        AxisTick()
        AxisValueLabel(format: chartDateFormat)
      }
    }
    .chartYAxis {
      AxisMarks { value in
        AxisGridLine()
        AxisTick()
        AxisValueLabel {
          let amount = value.as(Double.self) ?? 0
          DisplayCurrencyView.size12(
            symbol: "¥",
            amount: amount
          )
          .showingDecimals(false)
          .simpleFormat()
          .weight(symbol: .regular, integer: .regular)
          .color(Color.cBlack.opacity(0.6))
        }
      }
    }
    .chartOverlay { proxy in
      GeometryReader { geometry in
        Rectangle()
          .fill(.clear)
          .contentShape(Rectangle())
          .gesture(
            DragGesture(minimumDistance: 0)
              .onChanged { value in
                let x = value.location.x
                if let date = proxy.value(atX: x) as Date? {
                  if let closest = currentCardData.min(by: {
                    abs($0.date.timeIntervalSince(date)) < abs($1.date.timeIntervalSince(date))
                  }) {
                    withAnimation(.easeInOut(duration: 0.15)) {
                      viewModel.selectedCardPoint = closest
                    }
                  }
                }
              }
              .onEnded { _ in
                withAnimation(.easeInOut(duration: 0.15)) {
                  if let lastPoint = viewModel.selectedCardPoint {
                    viewModel.selectedCardPoint = currentCardData.first {
                      Calendar.current.isDate($0.date, inSameDayAs: lastPoint.date)
                    }
                  }
                }
              }
          )
      }
    }
  }

  /// 交易变动图表卡片
  @ViewBuilder
  private var transactionChartCard: some View {
    VStack(alignment: .leading, spacing: 12) {
      transactionChartHeader
      transactionDataDisplay
      transactionChart
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
  }

  /// 交易图表头部
  @ViewBuilder
  private var transactionChartHeader: some View {
    HStack {
      Text("交易变动")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.cBlack)
      Spacer()
      ForEach([TransactionChartType.expense, .income, .netFlow], id: \.self) { type in
        Button(action: {
          hapticManager.trigger(.selection)
          withAnimation {
            viewModel.selectTransactionType(type)
          }
        }) {
          Text(type.title)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(
              viewModel.selectedTransactionType == type ? type.color : Color.cBlack.opacity(0.6))
        }
      }
    }
  }

  /// 交易数据显示
  @ViewBuilder
  private var transactionDataDisplay: some View {
    HStack {
      Text(viewModel.selectedTransactionPoint?.date.formatted(.dateTime.day().month()) ?? "")
        .foregroundColor(.cBlack.opacity(0.6))
        .font(.system(size: 14))
      Spacer()
      DisplayCurrencyView.size14(
        symbol: "¥",
        amount: viewModel.selectedTransactionPoint?.amount ?? 0
      )
      .simpleFormat()
      .weight(symbol: .regular, integer: .regular)
      .color(Color.cBlack.opacity(0.6))
    }
    .contentTransition(.numericText(value: viewModel.selectedTransactionPoint?.amount ?? 0))
    .animation(.easeInOut(duration: 0.3), value: viewModel.selectedTransactionPoint?.amount)
  }

  /// 交易图表
  @ViewBuilder
  private var transactionChart: some View {
    Chart {
      ForEach(currentTransactionData) { dataPoint in
        LineMark(
          x: .value("日期", dataPoint.date),
          y: .value("金额", dataPoint.amount * viewModel.animationProgress)
        )
        .foregroundStyle(viewModel.selectedTransactionType.color)
        .interpolationMethod(.catmullRom)

        AreaMark(
          x: .value("日期", dataPoint.date),
          y: .value("金额", dataPoint.amount * viewModel.animationProgress)
        )
        .foregroundStyle(
          .linearGradient(
            colors: [
              viewModel.selectedTransactionType.color.opacity(0.3),
              viewModel.selectedTransactionType.color.opacity(0.1),
            ],
            startPoint: .top,
            endPoint: .bottom
          )
        )
        .interpolationMethod(.catmullRom)

        if let selected = viewModel.selectedTransactionPoint, selected.date == dataPoint.date {
          PointMark(
            x: .value("日期", dataPoint.date),
            y: .value("金额", dataPoint.amount)
          )
          .foregroundStyle(viewModel.selectedTransactionType.color)
          .symbolSize(100)
        }
      }
      if let selectedPoint = viewModel.selectedTransactionPoint {
        RuleMark(x: .value("Selected", selectedPoint.date))
          .foregroundStyle(Color.cBlack.opacity(0.3))
          .lineStyle(StrokeStyle(lineWidth: 1))
      }
    }
    .frame(height: 200)
    .chartXAxis {
      AxisMarks(values: .stride(by: chartTimeUnit, count: chartStepCount)) { value in
        AxisGridLine()
        AxisTick()
        AxisValueLabel(format: chartDateFormat)
      }
    }
    .chartYAxis {
      AxisMarks { value in
        AxisGridLine()
        AxisTick()
        AxisValueLabel {
          let amount = value.as(Double.self) ?? 0
          DisplayCurrencyView.size12(
            symbol: "¥",
            amount: amount
          )
          .showingDecimals(false)
          .simpleFormat()
          .weight(symbol: .regular, integer: .regular)
          .color(Color.cBlack.opacity(0.6))
        }
      }
    }
    .chartOverlay { proxy in
      GeometryReader { geometry in
        Rectangle()
          .fill(.clear)
          .contentShape(Rectangle())
          .gesture(
            DragGesture(minimumDistance: 0)
              .onChanged { value in
                let x = value.location.x
                if let date = proxy.value(atX: x) as Date? {
                  if let closest = currentTransactionData.min(by: {
                    abs($0.date.timeIntervalSince(date)) < abs($1.date.timeIntervalSince(date))
                  }) {
                    withAnimation(.easeInOut(duration: 0.15)) {
                      viewModel.selectedTransactionPoint = closest
                    }
                  }
                }
              }
              .onEnded { _ in
                withAnimation(.easeInOut(duration: 0.15)) {
                  if let lastPoint = viewModel.selectedTransactionPoint {
                    viewModel.selectedTransactionPoint = currentTransactionData.first {
                      Calendar.current.isDate($0.date, inSameDayAs: lastPoint.date)
                    }
                  }
                }
              }
          )
      }
    }
  }

}
