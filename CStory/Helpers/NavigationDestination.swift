//
//  NavigationDestinationHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import SwiftData
import SwiftUI

/// 应用中所有可导航的目的地
enum NavigationDestination: Hashable {

  // MARK: - 卡片管理

  /// 卡片分类测试页面
  case cardCategoryView
  /// 卡包页面（使用 DataManagement）
  case cardBagView
  /// 选择银行测试页面
  case selectBankView(isCredit: Bool, mainCategory: CardCategoryResponse)

  // MARK: - 交易相关

  /// 交易记录页面（使用 DataManagement）
  case transactionRecordView
  /// 交易详情页面（使用正式环境的TransactionDetailView）
  case transactionDetailView(UUID)
  /// 交易退款页面（使用正式环境的TransactionRefundView）
  case transactionRefundView(transaction: TransactionModel)

  // MARK: - 分类管理

  /// 交易分类设置页面
  case t_CategoryView
  /// 创建交易分类页面
  case createTransactionCategory(
    isMainCategory: Bool, mainCategoryId: String?, selectedType: TransactionType)
  /// 编辑交易分类页面
  case editTransactionCategoryView(categoryId: String, isMainCategory: Bool)
  /// 类别排序页面
  case categorySort(mode: CategorySortMode)

  // MARK: - 其他功能

  /// 汇率查看页面
  case currencyRateView

  // MARK: - Hashable 实现

  func hash(into hasher: inout Hasher) {
    switch self {
    case .cardCategoryView:
      hasher.combine(12)
    case .transactionDetailView(let transactionId):
      hasher.combine(1)
      hasher.combine(transactionId)
    case .transactionRefundView(let transaction):
      hasher.combine(2)
      hasher.combine(transaction.id)
    case .cardBagView:
      hasher.combine(10)
    case .selectBankView(let isCredit, let mainCategory):
      hasher.combine(13)
      hasher.combine(isCredit)
      hasher.combine(mainCategory.id)
    case .transactionRecordView:
      hasher.combine(11)

    case .t_CategoryView:
      hasher.combine(6)
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      hasher.combine(7)
      hasher.combine(isMainCategory)
      hasher.combine(mainCategoryId)
      hasher.combine(selectedType.rawValue)
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      hasher.combine(8)
      hasher.combine(categoryId)
      hasher.combine(isMainCategory)
    case .categorySort(let mode):
      hasher.combine(14)
      switch mode {
      case .mainCategory(let type):
        hasher.combine("main")
        hasher.combine(type.rawValue)
      case .subCategory(let mainCategoryId):
        hasher.combine("sub")
        hasher.combine(mainCategoryId)
      }
    case .currencyRateView:
      hasher.combine(9)
    }
  }

  static func == (lhs: NavigationDestination, rhs: NavigationDestination) -> Bool {
    switch (lhs, rhs) {
    case (.cardCategoryView, .cardCategoryView):
      return true
    case (
      .transactionDetailView(let lhsTransactionId), .transactionDetailView(let rhsTransactionId)
    ):
      return lhsTransactionId == rhsTransactionId
    case (.transactionRefundView(let lhsTransaction), .transactionRefundView(let rhsTransaction)):
      return lhsTransaction.id == rhsTransaction.id
    case (.cardBagView, .cardBagView):
      return true
    case (
      .selectBankView(let lhsIsCredit, let lhsMainCategory),
      .selectBankView(let rhsIsCredit, let rhsMainCategory)
    ):
      return lhsIsCredit == rhsIsCredit && lhsMainCategory.id == rhsMainCategory.id
    case (.transactionRecordView, .transactionRecordView):
      return true

    case (.t_CategoryView, .t_CategoryView):
      return true
    case (
      .createTransactionCategory(let lhsIsMainCategory, let lhsMainCategoryId, let lhsSelectedType),
      .createTransactionCategory(let rhsIsMainCategory, let rhsMainCategoryId, let rhsSelectedType)
    ):
      return lhsIsMainCategory == rhsIsMainCategory && lhsMainCategoryId == rhsMainCategoryId
        && lhsSelectedType == rhsSelectedType
    case (
      .editTransactionCategoryView(let lhsCategoryId, let lhsIsMainCategory),
      .editTransactionCategoryView(let rhsCategoryId, let rhsIsMainCategory)
    ):
      return lhsCategoryId == rhsCategoryId && lhsIsMainCategory == rhsIsMainCategory
    case (.categorySort(let lhsMode), .categorySort(let rhsMode)):
      switch (lhsMode, rhsMode) {
      case (.mainCategory(let lhsType), .mainCategory(let rhsType)):
        return lhsType == rhsType
      case (.subCategory(let lhsMainCategoryId), .subCategory(let rhsMainCategoryId)):
        return lhsMainCategoryId == rhsMainCategoryId
      default:
        return false
      }
    case (.currencyRateView, .currencyRateView):
      return true
    default:
      return false
    }
  }
}

// MARK: - View Generation

extension NavigationDestination {
  /// 返回每个导航目的地对应的视图
  /// - Parameter modelContext: SwiftData 模型上下文
  /// - Returns: 对应的 SwiftUI 视图
  @ViewBuilder
  func destinationView(modelContext: ModelContext) -> some View {
    switch self {
    case .cardCategoryView:
      CardCategoryView()
    case .transactionDetailView(let transactionId):
      TransactionDetailViewWrapper(transactionId: transactionId)
    case .transactionRefundView(let transaction):
      TransactionRefundViewWrapper(transaction: transaction)
    case .cardBagView:
      CardBagViewWrapper()
    case .selectBankView(let isCredit, let mainCategory):
      SelectBankView(
        isCredit: isCredit,
        mainCategory: mainCategory
      )
    case .transactionRecordView:
      TransactionRecordViewWrapper()

    case .t_CategoryView:
      TransactionCategoryView()
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      CategoryFormViewWrapper(
        mode: .create(
          isMainCategory: isMainCategory, mainCategoryId: mainCategoryId, selectedType: selectedType
        ))
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      CategoryFormViewWrapper(mode: .edit(categoryId: categoryId, isMainCategory: isMainCategory))
    case .categorySort(let mode):
      CategorySortViewWrapper(mode: mode)
    case .currencyRateView:
      CurrencyRateView()
    }
  }
}

/// TransactionDetailView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionDetailViewWrapper: View {
  let transactionId: UUID
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    // 根据transactionId找到对应的TransactionModel
    if let transaction = dataManager.allTransactions.first(where: { $0.id == transactionId }) {
      TransactionDetailView(transaction: transaction, dataManager: dataManager)
    } else {
      // 如果找不到交易，显示错误页面
      VStack(spacing: 20) {
        Image(systemName: "exclamationmark.triangle")
          .font(.system(size: 48))
          .foregroundColor(.orange)

        Text("交易不存在")
          .font(.title2)
          .fontWeight(.bold)

        Text("无法找到指定的交易记录")
          .font(.body)
          .foregroundColor(.secondary)
      }
      .padding()
      .navigationTitle("交易详情")
      .navigationBarTitleDisplayMode(.inline)
    }
  }
}

/// CardBagView 的包装器，用于从 Environment 获取 DataManagement
private struct CardBagViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    CardBagView(dataManager: dataManager)
  }
}

/// TransactionRecordView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionRecordViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper
  
  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  var body: some View {
    TransactionRecordView(
      viewModel: TransactionRecordVM(
        dataManager: dataManager,
        onTransactionTap: { transaction in
          // 震动反馈 - 统一使用.selection强度
          hapticManager.trigger(.selection)
          // 导航到交易详情页面
          pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
        }
      )
    )
  }
}

/// TransactionRefundView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionRefundViewWrapper: View {
  let transaction: TransactionModel
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    TransactionRefundView(transaction: transaction, dataManager: dataManager)
  }
}

/// CategoryFormView 的包装器，用于从 Environment 获取 DataManagement
private struct CategoryFormViewWrapper: View {
  let mode: CategoryFormMode
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CategoryFormView(mode: mode, dataManager: dataManager, modelContext: modelContext)
  }
}

/// CategorySortView 的包装器，用于从 Environment 获取 DataManagement
private struct CategorySortViewWrapper: View {
  let mode: CategorySortMode
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CategorySortView(
      viewModel: CategorySortVM(
        mode: mode,
        dataManager: dataManager,
        modelContext: modelContext
      )
    )
  }
}
